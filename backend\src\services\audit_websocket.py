"""
WebSocket service for real-time audit log broadcasting.

This module provides WebSocket functionality for broadcasting audit events
to connected clients in real-time using Flask-SocketIO.
"""

import logging
from typing import Dict, Any, Optional
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
from threading import Lock

logger = logging.getLogger(__name__)

class AuditWebSocketService:
    """Service for managing WebSocket connections and broadcasting audit events."""
    
    def __init__(self, socketio: SocketIO):
        self.socketio = socketio
        self.connected_clients = set()
        self.client_lock = Lock()
        
        # Register event handlers
        self._register_handlers()
        
    def _register_handlers(self):
        """Register SocketIO event handlers."""
        
        @self.socketio.on('connect', namespace='/ws/audit')
        def handle_connect():
            """Handle client connection to audit WebSocket."""
            try:
                client_id = self._get_client_id()
                with self.client_lock:
                    self.connected_clients.add(client_id)
                
                # Join the audit room for broadcasting
                join_room('audit_room', namespace='/ws/audit')
                
                logger.info(f"Client {client_id} connected to audit WebSocket. Total clients: {len(self.connected_clients)}")
                
                # Send connection confirmation
                emit('connection_status', {
                    'status': 'connected',
                    'message': 'Successfully connected to audit log stream'
                }, namespace='/ws/audit')
                
            except Exception as e:
                logger.error(f"Error handling client connection: {e}")
                emit('error', {'message': 'Connection failed'}, namespace='/ws/audit')
        
        @self.socketio.on('disconnect', namespace='/ws/audit')
        def handle_disconnect():
            """Handle client disconnection from audit WebSocket."""
            try:
                client_id = self._get_client_id()
                with self.client_lock:
                    self.connected_clients.discard(client_id)
                
                # Leave the audit room
                leave_room('audit_room', namespace='/ws/audit')
                
                logger.info(f"Client {client_id} disconnected from audit WebSocket. Total clients: {len(self.connected_clients)}")
                
            except Exception as e:
                logger.error(f"Error handling client disconnection: {e}")
        
        @self.socketio.on('ping', namespace='/ws/audit')
        def handle_ping():
            """Handle ping from client for connection health check."""
            emit('pong', {'timestamp': self._get_current_timestamp()}, namespace='/ws/audit')
    
    def broadcast_audit_event(self, event_data: Dict[str, Any]) -> None:
        """
        Broadcast an audit event to all connected clients.
        
        Args:
            event_data: Dictionary containing audit event information
                       Expected format:
                       {
                           "timestamp": "2025-08-13T17:30:12Z",
                           "event_type": "generate",
                           "resource_id": "report_1234", 
                           "user": "system",
                           "details": "Report generated successfully"
                       }
        """
        try:
            if not self.connected_clients:
                logger.debug("No clients connected, skipping audit event broadcast")
                return
            
            # Transform the event data to match the expected format
            formatted_event = self._format_audit_event(event_data)
            
            # Broadcast to all clients in the audit room
            self.socketio.emit(
                'audit_event',
                formatted_event,
                room='audit_room',
                namespace='/ws/audit'
            )
            
            logger.debug(f"Broadcasted audit event to {len(self.connected_clients)} clients: {formatted_event['event_type']}")
            
        except Exception as e:
            logger.error(f"Error broadcasting audit event: {e}")
    
    def _format_audit_event(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format audit event data for WebSocket transmission.
        
        Args:
            event_data: Raw audit event data from audit.py
            
        Returns:
            Formatted event data for WebSocket clients
        """
        # Extract resource_id from details if available
        resource_id = None
        details_str = ""
        
        if 'details' in event_data and isinstance(event_data['details'], dict):
            details = event_data['details']
            # Try to extract resource_id from common fields
            resource_id = (
                details.get('report_id') or 
                details.get('template_id') or 
                details.get('resource_id') or
                'unknown'
            )
            # Create a readable details string
            details_str = ', '.join([f"{k}: {v}" for k, v in details.items()])
        
        return {
            'timestamp': event_data.get('timestamp', self._get_current_timestamp()),
            'event_type': event_data.get('action', 'unknown'),
            'resource_id': resource_id,
            'user': event_data.get('user', 'anonymous'),
            'details': details_str or event_data.get('action', 'No details available')
        }
    
    def _get_client_id(self) -> str:
        """Get a unique identifier for the current client."""
        from flask import request
        return f"{request.sid}"
    
    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        from datetime import datetime
        return datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')
    
    def get_connected_clients_count(self) -> int:
        """Get the number of currently connected clients."""
        with self.client_lock:
            return len(self.connected_clients)


# Global instance to be initialized in main.py
audit_websocket_service: Optional[AuditWebSocketService] = None


def initialize_audit_websocket(socketio: SocketIO) -> AuditWebSocketService:
    """
    Initialize the global audit WebSocket service.
    
    Args:
        socketio: Flask-SocketIO instance
        
    Returns:
        Initialized AuditWebSocketService instance
    """
    global audit_websocket_service
    audit_websocket_service = AuditWebSocketService(socketio)
    logger.info("Audit WebSocket service initialized")
    return audit_websocket_service


def get_audit_websocket_service() -> Optional[AuditWebSocketService]:
    """
    Get the global audit WebSocket service instance.
    
    Returns:
        AuditWebSocketService instance or None if not initialized
    """
    return audit_websocket_service


def broadcast_audit_event(event_data: Dict[str, Any]) -> None:
    """
    Convenience function to broadcast audit events.
    
    Args:
        event_data: Audit event data to broadcast
    """
    service = get_audit_websocket_service()
    if service:
        service.broadcast_audit_event(event_data)
    else:
        logger.warning("Audit WebSocket service not initialized, cannot broadcast event")
