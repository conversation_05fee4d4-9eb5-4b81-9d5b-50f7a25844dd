import {
  require_react
} from "./chunk-X5X7WI57.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs
function composeEventHandlers(originalEvent<PERSON>andler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {
  return function handleEvent(event) {
    originalEventHandler == null ? void 0 : originalEventHandler(event);
    if (checkForDefaultPrevented === false || !event.defaultPrevented) {
      return ourEventHandler == null ? void 0 : ourEventHandler(event);
    }
  };
}

// node_modules/.pnpm/@radix-ui+react-use-layout-_b468d9cfa806abf8ca2420b05f266f6a/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs
var React = __toESM(require_react(), 1);
var useLayoutEffect2 = (globalThis == null ? void 0 : globalThis.document) ? React.useLayoutEffect : () => {
};

// node_modules/.pnpm/@radix-ui+react-use-size@1._47d06e3ed49a54d1b87474532164591d/node_modules/@radix-ui/react-use-size/dist/index.mjs
var React2 = __toESM(require_react(), 1);
function useSize(element) {
  const [size, setSize] = React2.useState(void 0);
  useLayoutEffect2(() => {
    if (element) {
      setSize({ width: element.offsetWidth, height: element.offsetHeight });
      const resizeObserver = new ResizeObserver((entries) => {
        if (!Array.isArray(entries)) {
          return;
        }
        if (!entries.length) {
          return;
        }
        const entry = entries[0];
        let width;
        let height;
        if ("borderBoxSize" in entry) {
          const borderSizeEntry = entry["borderBoxSize"];
          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;
          width = borderSize["inlineSize"];
          height = borderSize["blockSize"];
        } else {
          width = element.offsetWidth;
          height = element.offsetHeight;
        }
        setSize({ width, height });
      });
      resizeObserver.observe(element, { box: "border-box" });
      return () => resizeObserver.unobserve(element);
    } else {
      setSize(void 0);
    }
  }, [element]);
  return size;
}

// node_modules/.pnpm/@radix-ui+react-use-control_aba99c23fedbfe0aa9cdb40a73f261a1/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs
var React4 = __toESM(require_react(), 1);
var React22 = __toESM(require_react(), 1);

// node_modules/.pnpm/@radix-ui+react-use-effect-_bdfd70ceff1e9a0f8db1ddad290341ec/node_modules/@radix-ui/react-use-effect-event/dist/index.mjs
var React3 = __toESM(require_react(), 1);
var useReactEffectEvent = React3[" useEffectEvent ".trim().toString()];
var useReactInsertionEffect = React3[" useInsertionEffect ".trim().toString()];

// node_modules/.pnpm/@radix-ui+react-use-control_aba99c23fedbfe0aa9cdb40a73f261a1/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs
var useInsertionEffect = React4[" useInsertionEffect ".trim().toString()] || useLayoutEffect2;
function useControllableState({
  prop,
  defaultProp,
  onChange = () => {
  },
  caller
}) {
  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({
    defaultProp,
    onChange
  });
  const isControlled = prop !== void 0;
  const value = isControlled ? prop : uncontrolledProp;
  if (true) {
    const isControlledRef = React4.useRef(prop !== void 0);
    React4.useEffect(() => {
      const wasControlled = isControlledRef.current;
      if (wasControlled !== isControlled) {
        const from = wasControlled ? "controlled" : "uncontrolled";
        const to = isControlled ? "controlled" : "uncontrolled";
        console.warn(
          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`
        );
      }
      isControlledRef.current = isControlled;
    }, [isControlled, caller]);
  }
  const setValue = React4.useCallback(
    (nextValue) => {
      var _a;
      if (isControlled) {
        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;
        if (value2 !== prop) {
          (_a = onChangeRef.current) == null ? void 0 : _a.call(onChangeRef, value2);
        }
      } else {
        setUncontrolledProp(nextValue);
      }
    },
    [isControlled, prop, setUncontrolledProp, onChangeRef]
  );
  return [value, setValue];
}
function useUncontrolledState({
  defaultProp,
  onChange
}) {
  const [value, setValue] = React4.useState(defaultProp);
  const prevValueRef = React4.useRef(value);
  const onChangeRef = React4.useRef(onChange);
  useInsertionEffect(() => {
    onChangeRef.current = onChange;
  }, [onChange]);
  React4.useEffect(() => {
    var _a;
    if (prevValueRef.current !== value) {
      (_a = onChangeRef.current) == null ? void 0 : _a.call(onChangeRef, value);
      prevValueRef.current = value;
    }
  }, [value, prevValueRef]);
  return [value, setValue, onChangeRef];
}
function isFunction(value) {
  return typeof value === "function";
}
var SYNC_STATE = Symbol("RADIX:SYNC_STATE");

// node_modules/.pnpm/@radix-ui+react-use-previou_51a8dfff93c1d659dda076e8513111a4/node_modules/@radix-ui/react-use-previous/dist/index.mjs
var React5 = __toESM(require_react(), 1);
function usePrevious(value) {
  const ref = React5.useRef({ value, previous: value });
  return React5.useMemo(() => {
    if (ref.current.value !== value) {
      ref.current.previous = ref.current.value;
      ref.current.value = value;
    }
    return ref.current.previous;
  }, [value]);
}

export {
  composeEventHandlers,
  useLayoutEffect2,
  useSize,
  useControllableState,
  usePrevious
};
//# sourceMappingURL=chunk-Q2SEDPHJ.js.map
