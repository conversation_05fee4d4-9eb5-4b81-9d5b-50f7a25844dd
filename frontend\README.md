# Indian Railways Report Generator - Frontend

A responsive React application for generating Indian Railways reports (Joint Report and TA Form) with template management, document generation, and PDF preview capabilities.

## Features

### 🎯 Core Functionality
- **Template Management**: Upload, view, and manage DOCX templates
- **Report Generation**: Dynamic form generation from template placeholders
- **Document Preview**: PDF preview with iframe viewer
- **Multi-format Download**: Support for both PDF and DOCX formats
- **Responsive Design**: Mobile-first responsive UI

### 📱 User Interface
- **Dashboard**: Overview with quick actions and feature highlights
- **Templates Page**: Upload templates, view placeholders, manage templates
- **Generate Page**: Select template, fill form, generate reports
- **Reports Page**: View, preview, and download generated reports

### 🛠 Technical Features
- **Error Boundaries**: Comprehensive error handling with user-friendly messages
- **Loading States**: Proper loading indicators throughout the application
- **Toast Notifications**: Real-time feedback for user actions
- **File Upload**: Drag-and-drop file upload with validation
- **API Integration**: Centralized API service with error handling

## Technology Stack

- **Framework**: React 19.1.0 with Vite
- **Styling**: Tailwind CSS 4.1.7
- **UI Components**: Radix UI primitives with custom styling
- **Routing**: React Router DOM 7.6.1
- **Forms**: React Hook Form with validation
- **Icons**: Lucide React
- **Notifications**: React Hot Toast
- **PDF Handling**: React PDF for preview
- **HTTP Client**: Fetch API with custom wrapper

## Project Structure

```
src/
├── components/
│   ├── shared/           # Reusable components
│   │   ├── FileUploader.jsx
│   │   ├── PDFPreview.jsx
│   │   ├── LoadingSpinner.jsx
│   │   ├── Modal.jsx
│   │   └── index.js
│   ├── ui/              # UI primitives (Radix UI)
│   ├── ErrorBoundary.jsx
│   └── Header.jsx
├── pages/               # Main application pages
│   ├── TemplatesPage.jsx
│   ├── GeneratePage.jsx
│   ├── ReportsPage.jsx
│   └── index.js
├── lib/                 # Utilities and services
│   ├── api.js          # API service layer
│   └── utils.js        # Utility functions
├── hooks/              # Custom React hooks
└── App.jsx             # Main application component
```

## API Integration

The frontend integrates with the backend through a centralized API service (`src/lib/api.js`) that provides:

- **Templates API**: Upload, list, get placeholders, delete templates
- **Reports API**: Generate, list, download, preview reports
- **Error Handling**: Consistent error handling with user feedback
- **File Operations**: Upload and download with progress tracking

## Key Components

### Shared Components

1. **FileUploader**: Drag-and-drop file upload with validation
2. **PDFPreview**: PDF viewer with download and external link options
3. **LoadingSpinner**: Various loading states (inline, page, section)
4. **Modal**: Reusable modal with confirmation and info variants

### Pages

1. **TemplatesPage**: Template management with upload and placeholder viewing
2. **GeneratePage**: Report generation with dynamic form creation
3. **ReportsPage**: Report listing with preview and download options

## Responsive Design

The application is fully responsive with:
- Mobile-first approach using Tailwind CSS
- Responsive navigation with hidden labels on small screens
- Horizontal scrolling tables on mobile
- Flexible grid layouts that adapt to screen size
- Touch-friendly interface elements

## Error Handling

Comprehensive error handling includes:
- **Error Boundaries**: Catch and display React errors gracefully
- **API Error Handling**: Centralized error handling with user-friendly messages
- **Loading States**: Proper loading indicators for all async operations
- **Toast Notifications**: Real-time feedback for user actions
- **Validation**: Form validation with helpful error messages

## Environment Configuration

Create a `.env` file in the frontend directory:

```env
# API Configuration
VITE_API_BASE_URL=/api

# Development settings
VITE_NODE_ENV=development
```

## Development

```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Build for production
pnpm build

# Preview production build
pnpm preview
```

## Browser Support

- Modern browsers with ES2015+ support
- Mobile browsers (iOS Safari, Chrome Mobile)
- Desktop browsers (Chrome, Firefox, Safari, Edge)

## Performance Considerations

- Code splitting with dynamic imports
- Optimized bundle size with tree shaking
- Lazy loading of components where appropriate
- Efficient re-rendering with React best practices

## Accessibility

- Semantic HTML structure
- Keyboard navigation support
- Screen reader friendly
- High contrast support
- Focus management in modals

## Future Enhancements

- Offline support with service workers
- Real-time collaboration features
- Advanced PDF editing capabilities
- Template versioning system
- Bulk operations for reports
