#!/usr/bin/env python3
"""
Test script for the audit logs endpoint
"""
import sys
import os
import json

# Add the backend src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from routes.audit import _read_audit_logs_safely, _filter_logs, _sort_logs, _paginate_logs

def test_audit_functions():
    """Test the audit log functions"""
    print("Testing audit log functions...")
    
    # Test reading logs
    print("\n1. Testing _read_audit_logs_safely()...")
    logs = _read_audit_logs_safely()
    print(f"   Found {len(logs)} log entries")
    
    if logs:
        print(f"   First log: {logs[0]}")
        print(f"   Last log: {logs[-1]}")
    
    # Test filtering
    print("\n2. Testing _filter_logs()...")
    filtered = _filter_logs(logs, 'report')
    print(f"   Found {len(filtered)} entries matching 'report'")
    
    filtered_template = _filter_logs(logs, 'template')
    print(f"   Found {len(filtered_template)} entries matching 'template'")
    
    # Test sorting
    print("\n3. Testing _sort_logs()...")
    sorted_asc = _sort_logs(logs, 'asc')
    sorted_desc = _sort_logs(logs, 'desc')
    print(f"   Sorted ascending: {len(sorted_asc)} entries")
    print(f"   Sorted descending: {len(sorted_desc)} entries")
    
    if sorted_asc and sorted_desc:
        print(f"   First entry (asc): {sorted_asc[0]['timestamp']}")
        print(f"   First entry (desc): {sorted_desc[0]['timestamp']}")
    
    # Test pagination
    print("\n4. Testing _paginate_logs()...")
    paginated = _paginate_logs(logs, 1, 5)
    print(f"   Page 1 with limit 5:")
    print(f"   Total: {paginated['total']}")
    print(f"   Pages: {paginated['pages']}")
    print(f"   Results: {len(paginated['results'])}")
    
    # Test with different page
    if paginated['pages'] > 1:
        paginated_2 = _paginate_logs(logs, 2, 5)
        print(f"   Page 2 results: {len(paginated_2['results'])}")
    
    print("\n✅ All audit function tests completed!")
    return True

def test_endpoint_simulation():
    """Simulate the endpoint behavior"""
    print("\n" + "="*50)
    print("Simulating GET /api/audit/logs endpoint...")
    
    # Simulate different query parameters
    test_cases = [
        {"page": 1, "limit": 10, "sort": "desc", "search": ""},
        {"page": 1, "limit": 5, "sort": "asc", "search": "report"},
        {"page": 2, "limit": 3, "sort": "desc", "search": ""},
        {"page": 1, "limit": 50, "sort": "desc", "search": "template"},
    ]
    
    for i, params in enumerate(test_cases, 1):
        print(f"\nTest case {i}: {params}")
        
        # Read logs
        logs = _read_audit_logs_safely()
        
        # Apply search filter
        if params.get('search'):
            logs = _filter_logs(logs, params['search'])
        
        # Sort logs
        logs = _sort_logs(logs, params['sort'])
        
        # Paginate results
        result = _paginate_logs(logs, params['page'], params['limit'])
        
        print(f"   Result: {result['total']} total, page {result['page']} of {result['pages']}, {len(result['results'])} results")
        
        # Show first result if available
        if result['results']:
            first_result = result['results'][0]
            print(f"   First result: {first_result['action']} at {first_result['timestamp']}")
    
    print("\n✅ Endpoint simulation completed!")

if __name__ == '__main__':
    print("Audit Logs Endpoint Test")
    print("=" * 50)
    
    try:
        # Test the individual functions
        test_audit_functions()
        
        # Test the endpoint simulation
        test_endpoint_simulation()
        
        print("\n🎉 All tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
