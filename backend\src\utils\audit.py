import json
import logging
import os
from datetime import datetime, timezone
from typing import Any, Dict, Optional

import portalocker


logger = logging.getLogger(__name__)

# Base directory for backend data (same as where reports/documents live)
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
AUDIT_LOG_PATH = os.path.join(BASE_DIR, 'audit.log')


def _ensure_parent_directory_exists(file_path: str) -> None:
    directory = os.path.dirname(file_path)
    if directory and not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)


def _utc_now_iso8601_z() -> str:
    # Ensure Z suffix rather than +00:00
    return datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z')


def audit_event(action: str, user: Optional[str], details: Optional[Dict[str, Any]] = None) -> None:
    """Append a single audit event as JSON to the audit log.

    - Writes a single line JSON object to a JSONL file at AUDIT_LOG_PATH
    - Uses an exclusive file lock to be concurrency-safe
    - Broadcasts event via WebSocket to connected clients
    - Swallows exceptions and logs warnings; never raises
    """
    try:
        event = {
            'timestamp': _utc_now_iso8601_z(),
            'action': str(action),
            'user': user if user else 'anonymous',
            'details': details if details is not None else {},
        }

        _ensure_parent_directory_exists(AUDIT_LOG_PATH)

        # Use portalocker for cross-platform file locking during append
        with portalocker.Lock(AUDIT_LOG_PATH, mode='a', flags=portalocker.LOCK_EX, timeout=5) as locked_file:
            locked_file.write(json.dumps(event, ensure_ascii=False) + '\n')
            locked_file.flush()
            try:
                os.fsync(locked_file.fileno())
            except Exception:
                # fsync may not be available on some environments; ignore
                pass

        # Broadcast event via WebSocket to connected clients
        try:
            from src.services.audit_websocket import broadcast_audit_event
            broadcast_audit_event(event)
        except Exception as ws_exc:
            # WebSocket broadcasting is non-critical; log but don't fail
            try:
                logger.debug('Failed to broadcast audit event via WebSocket: %s', ws_exc)
            except Exception:
                pass
    except Exception as exc:
        # Non-fatal; record as a warning so it doesn't affect request handling
        try:
            logger.warning('Failed to write audit event %s: %s', action, exc)
        except Exception:
            # Last-ditch: avoid any cascading failures from logging itself
            pass


if __name__ == '__main__':
    print('Writing a few test audit entries to', AUDIT_LOG_PATH)
    audit_event('test_event_start', 'anonymous', {'note': 'begin'})
    audit_event('test_event_action', None, {'key': 'value', 'n': 1})
    audit_event('test_event_end', 'tester', None)

    print('\nCurrent audit.log contents:')
    try:
        with open(AUDIT_LOG_PATH, 'r', encoding='utf-8') as f:
            print(f.read())
    except FileNotFoundError:
        print('No audit.log file found.')


