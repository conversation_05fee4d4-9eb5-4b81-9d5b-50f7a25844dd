#!/usr/bin/env python3
"""
Test script to simulate audit events for WebSocket testing.

This script generates various audit events to test the real-time
WebSocket functionality of the audit log system.
"""

import sys
import os
import time
import random
from datetime import datetime

# Add the backend src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.audit import audit_event

def generate_test_events():
    """Generate various test audit events."""
    
    # Sample data for realistic events
    users = ['john_doe', 'jane_smith', 'admin', 'system', 'test_user']
    templates = ['template_001', 'template_002', 'template_003', 'joint_report_template', 'ta_form_template']
    reports = ['report_001', 'report_002', 'report_003', 'report_004', 'report_005']
    
    event_types = [
        ('template_uploaded', lambda: {
            'template_id': random.choice(templates),
            'filename': f'template_{random.randint(1, 100)}.docx',
            'placeholders_count': random.randint(5, 20)
        }),
        ('report_generated', lambda: {
            'report_id': random.choice(reports),
            'template_id': random.choice(templates),
            'format': random.choice(['docx', 'pdf'])
        }),
        ('report_downloaded', lambda: {
            'report_id': random.choice(reports),
            'format': random.choice(['docx', 'pdf']),
            'user_agent': 'Mozilla/5.0 (Test Browser)'
        }),
        ('report_signed', lambda: {
            'report_id': random.choice(reports),
            'num_signatures': random.randint(1, 3)
        }),
        ('template_deleted', lambda: {
            'template_id': random.choice(templates),
            'reason': 'User requested deletion'
        }),
        ('report_preview', lambda: {
            'report_id': random.choice(reports),
            'format': 'pdf'
        }),
        ('user_created', lambda: {
            'username': f'user_{random.randint(1, 1000)}',
            'email': f'user{random.randint(1, 1000)}@example.com'
        }),
        ('user_updated', lambda: {
            'username': random.choice(users),
            'changes': random.choice(['profile', 'password', 'permissions'])
        })
    ]
    
    print("🚀 Starting WebSocket audit event simulation...")
    print("📋 This will generate various audit events to test real-time updates")
    print("🔄 Events will be generated every 2-5 seconds")
    print("⏹️  Press Ctrl+C to stop\n")
    
    event_count = 0
    
    try:
        while True:
            # Choose random event type
            event_type, details_generator = random.choice(event_types)
            user = random.choice(users)
            details = details_generator()
            
            # Generate the audit event
            audit_event(event_type, user, details)
            
            event_count += 1
            timestamp = datetime.now().strftime('%H:%M:%S')
            
            print(f"[{timestamp}] 📝 Event #{event_count}: {event_type} by {user}")
            print(f"    Details: {details}")
            
            # Random delay between events (2-5 seconds)
            delay = random.uniform(2, 5)
            time.sleep(delay)
            
    except KeyboardInterrupt:
        print(f"\n✅ Simulation stopped. Generated {event_count} events total.")
        print("🔍 Check your audit page to see if events appeared in real-time!")

def generate_burst_events(count=10):
    """Generate a burst of events for performance testing."""
    
    print(f"🚀 Generating {count} events in quick succession for performance testing...")
    
    for i in range(count):
        audit_event(
            f'performance_test_{i}',
            'test_user',
            {
                'test_id': f'perf_test_{i}',
                'batch_number': i + 1,
                'total_events': count,
                'timestamp': datetime.now().isoformat()
            }
        )
        print(f"Generated event {i + 1}/{count}")
        time.sleep(0.1)  # Small delay to avoid overwhelming
    
    print("✅ Burst test completed!")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == 'burst':
        count = int(sys.argv[2]) if len(sys.argv) > 2 else 10
        generate_burst_events(count)
    else:
        generate_test_events()
