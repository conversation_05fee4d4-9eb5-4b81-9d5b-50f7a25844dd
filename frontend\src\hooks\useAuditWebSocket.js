import { useState, useEffect, useRef, useCallback } from 'react'
import { io } from 'socket.io-client'
import toast from 'react-hot-toast'

const WEBSOCKET_URL = 'http://localhost:6000'
const NAMESPACE = '/ws/audit'
const RECONNECT_ATTEMPTS = 5
const RECONNECT_DELAY = 3000

/**
 * Custom hook for managing WebSocket connection to audit log events
 * 
 * @param {boolean} enabled - Whether to enable the WebSocket connection
 * @param {function} onAuditEvent - Callback function when new audit event is received
 * @returns {object} WebSocket connection state and controls
 */
export const useAuditWebSocket = (enabled = false, onAuditEvent = null) => {
  const [isConnected, setIsConnected] = useState(false)
  const [connectionError, setConnectionError] = useState(null)
  const [reconnectAttempts, setReconnectAttempts] = useState(0)
  const socketRef = useRef(null)
  const reconnectTimeoutRef = useRef(null)

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (socketRef.current?.connected) {
      return // Already connected
    }

    try {
      console.log('Connecting to audit WebSocket...')
      
      const socket = io(WEBSOCKET_URL + NAMESPACE, {
        transports: ['websocket', 'polling'],
        timeout: 10000,
        forceNew: true,
        reconnection: true,
        reconnectionAttempts: RECONNECT_ATTEMPTS,
        reconnectionDelay: RECONNECT_DELAY,
      })

      // Connection successful
      socket.on('connect', () => {
        console.log('Connected to audit WebSocket')
        setIsConnected(true)
        setConnectionError(null)
        setReconnectAttempts(0)
        
        toast.success('Connected to live audit updates', {
          duration: 2000,
          position: 'bottom-right'
        })
      })

      // Connection failed
      socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error)
        setIsConnected(false)
        setConnectionError(error.message || 'Connection failed')
        
        // Attempt reconnection
        if (reconnectAttempts < RECONNECT_ATTEMPTS) {
          setReconnectAttempts(prev => prev + 1)
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log(`Reconnection attempt ${reconnectAttempts + 1}/${RECONNECT_ATTEMPTS}`)
            connect()
          }, RECONNECT_DELAY)
        } else {
          toast.error('Failed to connect to live updates', {
            duration: 4000,
            position: 'bottom-right'
          })
        }
      })

      // Disconnected
      socket.on('disconnect', (reason) => {
        console.log('Disconnected from audit WebSocket:', reason)
        setIsConnected(false)
        
        if (reason === 'io server disconnect') {
          // Server disconnected, try to reconnect
          socket.connect()
        }
        
        toast('Disconnected from live updates', {
          duration: 3000,
          position: 'bottom-right'
        })
      })

      // Connection status updates
      socket.on('connection_status', (data) => {
        console.log('Connection status:', data)
      })

      // Audit event received
      socket.on('audit_event', (eventData) => {
        console.log('Received audit event:', eventData)
        
        // Show toast notification
        const eventType = eventData.event_type || 'unknown'
        toast(`New audit event: ${eventType}`, {
          duration: 3000,
          position: 'bottom-right',
          icon: '📋'
        })
        
        // Call the callback if provided
        if (onAuditEvent && typeof onAuditEvent === 'function') {
          onAuditEvent(eventData)
        }
      })

      // Handle ping/pong for connection health
      socket.on('pong', (data) => {
        console.log('Received pong:', data)
      })

      // Error handling
      socket.on('error', (error) => {
        console.error('WebSocket error:', error)
        setConnectionError(error.message || 'WebSocket error')
        toast.error('WebSocket error occurred', {
          duration: 3000,
          position: 'bottom-right'
        })
      })

      socketRef.current = socket

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
      setConnectionError(error.message)
      toast.error('Failed to initialize WebSocket connection', {
        duration: 4000,
        position: 'bottom-right'
      })
    }
  }, [reconnectAttempts, onAuditEvent])

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    if (socketRef.current) {
      console.log('Disconnecting from audit WebSocket...')
      socketRef.current.disconnect()
      socketRef.current = null
    }

    setIsConnected(false)
    setConnectionError(null)
    setReconnectAttempts(0)
  }, [])

  // Send ping to check connection health
  const ping = useCallback(() => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('ping')
    }
  }, [])

  // Effect to handle connection based on enabled state
  useEffect(() => {
    if (enabled) {
      connect()
    } else {
      disconnect()
    }

    // Cleanup on unmount
    return () => {
      disconnect()
    }
  }, [enabled, connect, disconnect])

  // Periodic ping to maintain connection health
  useEffect(() => {
    if (isConnected) {
      const pingInterval = setInterval(ping, 30000) // Ping every 30 seconds
      return () => clearInterval(pingInterval)
    }
  }, [isConnected, ping])

  return {
    isConnected,
    connectionError,
    reconnectAttempts,
    connect,
    disconnect,
    ping
  }
}
