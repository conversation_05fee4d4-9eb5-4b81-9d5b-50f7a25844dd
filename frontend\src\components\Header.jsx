import { Link, useLocation } from 'react-router-dom'
import { FileText, List, Plus, Settings, Clock } from 'lucide-react'
import { Button } from '@/components/ui/button'

const Header = () => {
  const location = useLocation()

  return (
    <header className="bg-white shadow-sm border-b sticky top-0 z-40">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-3 sm:py-4">
        <div className="flex items-center justify-between">
          <Link to="/" className="flex items-center space-x-2">
            <FileText className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600" />
            <span className="text-lg sm:text-xl font-bold text-gray-900 hidden xs:block">
              <span className="hidden sm:inline">Railways </span>Report Generator
            </span>
          </Link>
          
          <nav className="flex items-center space-x-1 sm:space-x-2">
            <Link to="/">
              <Button
                variant={location.pathname === '/' ? 'default' : 'ghost'}
                size="sm"
                className="flex items-center space-x-2"
              >
                <FileText className="h-4 w-4" />
                <span className="hidden sm:inline">Dashboard</span>
              </Button>
            </Link>

            <Link to="/templates">
              <Button
                variant={location.pathname === '/templates' ? 'default' : 'ghost'}
                size="sm"
                className="flex items-center space-x-2"
              >
                <Settings className="h-4 w-4" />
                <span className="hidden sm:inline">Templates</span>
              </Button>
            </Link>

            <Link to="/generate">
              <Button
                variant={location.pathname === '/generate' ? 'default' : 'ghost'}
                size="sm"
                className="flex items-center space-x-2"
              >
                <Plus className="h-4 w-4" />
                <span className="hidden sm:inline">Generate</span>
              </Button>
            </Link>

            <Link to="/reports">
              <Button
                variant={location.pathname === '/reports' ? 'default' : 'ghost'}
                size="sm"
                className="flex items-center space-x-2"
              >
                <List className="h-4 w-4" />
                <span className="hidden sm:inline">Reports</span>
              </Button>
            </Link>

            <Link to="/audit">
              <Button
                variant={location.pathname === '/audit' ? 'default' : 'ghost'}
                size="sm"
                className="flex items-center space-x-2"
              >
                <Clock className="h-4 w-4" />
                <span className="hidden sm:inline">Audit</span>
              </Button>
            </Link>
          </nav>
        </div>
      </div>
    </header>
  )
}

export default Header

